<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx">
        <testCase name="RAAproval renders loading state" duration="28"/>
        <testCase name="RAAproval renders error state" duration="5"/>
        <testCase name="RAAproval renders no data state" duration="4"/>
        <testCase name="RAAproval handles Cancel button" duration="5"/>
        <testCase name="RAAproval handles template fetch error" duration="2"/>
        <testCase name="RAAproval additional coverage calls updateSavedRA on Save button click" duration="5"/>
        <testCase name="RAAproval additional coverage shows Level of RA dropdown and handles Save for non-ROUTINE" duration="6"/>
        <testCase name="RAAproval additional coverage handles categories/hazards not matching template" duration="3"/>
        <testCase name="RAAproval additional coverage handles ROUTINE level with modal confirm" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles generatePDF success case" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles dropdown onChange with empty value" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles Cancel button in preview mode" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles beforeunload event listener setup and cleanup" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with different data scenarios" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with template data mismatch" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with ROUTINE level and action date" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles form data processing with different field types" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with missing parameters" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles ROUTINE level selection and save button state" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with invalid parameters (no levelOfRA)" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with ROUTINE but no actionDate" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with text field changes" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with assessment changes" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles previewOnly calculation with different user scenarios" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles window beforeunload event properly" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles different approval status scenarios" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles edge case with empty risk_approver array" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles risk parameter data processing with complex data" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with null/undefined parameter types" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles setDataStore call in loadBasicDetails" duration="2"/>
    </file>
</testExecutions>